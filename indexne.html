<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>User Categories Cloud</title>
<style>
  body {
    font-family: Arial, sans-serif;
    background: #f7f7f7;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
  }

  .cloud {
    position: relative;
    width: 100vw;
    height: 100vh;
  }

  .tag {
    position: absolute;
    padding: 8px 14px;
    border-radius: 8px;
    color: #fff;
    font-weight: bold;
    white-space: nowrap;
    transition: transform 0.2s ease;
  }

  .tag:hover {
    transform: scale(1.1);
    cursor: pointer;
  }
</style>
</head>
<body>

<div class="cloud" id="cloud"></div>

<script>
  const categories = [
    { name: "UX/UI Designer", count: 39 },
    { name: "Software Engineer", count: 33 },
    { name: "Designer", count: 11 },
    { name: "Frontend Developer", count: 9 },
    { name: "Professional", count: 8 },
    { name: "Web Developer", count: 7 },
    { name: "Student", count: 7 },
    { name: "Marketing Specialist", count: 7 },
    { name: "Mobile Developer", count: 6 },
    { name: "Graphic Designer", count: 6 },
    { name: "Freelancer", count: 5 },
    { name: "Product Owner", count: 4 },
    { name: "Penetration Tester", count: 4 },
    { name: "Data Scientist", count: 4 },
    { name: "Business Owner", count: 4 },
    { name: "Software Developer", count: 3 },
    { name: "DevOps Engineer", count: 3 },
    { name: "Cybersecurity Specialist", count: 3 },
    { name: "Consultant", count: 2 },
    { name: "IT Project Manager", count: 2 },
    { name: "Brand Manager", count: 2 },
    { name: "Video Editor", count: 2 },
    { name: "Project Manager", count: 2 },
    { name: "Coach", count: 1 },
    { name: "Sales Manager", count: 1 },
    { name: "Data Analyst", count: 1 },
    { name: "C# Developer", count: 1 },
    { name: "Backend Developer", count: 1 },
    { name: "SOC Analyst", count: 1 },
    { name: "Agile Coach", count: 1 },
    { name: "Game Designer", count: 1 },
    { name: "Legal Counsel", count: 1 },
    { name: "QA Engineer", count: 1 },
    { name: "Cross-Platform Mobile Developer", count: 1 },
    { name: "Content Writer", count: 1 },
    { name: "Flutter Developer", count: 1 },
    { name: "Data Engineer", count: 1 },
    { name: "Quality Assurance Engineer", count: 1 },
    { name: "iOS Developer", count: 1 },
    { name: "React Developer", count: 1 },
    { name: "Business Analyst", count: 1 },
    { name: "PR Specialist", count: 1 },
    { name: "Scrum Master", count: 1 },
    { name: "AI/ML Engineer", count: 1 },
    { name: "Python Developer", count: 1 }
  ];

  const minFont = 12;
  const maxFont = 40;
  const maxCount = Math.max(...categories.map(c => c.count));
  const minCount = Math.min(...categories.map(c => c.count));

  function getRandomColor() {
    return `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`;
  }

  // Store positions of placed tags to avoid overlaps
  const placedTags = [];

  function getTextWidth(text, fontSize) {
    // Approximate text width calculation
    return text.length * fontSize * 0.6 + 28; // 28px for padding
  }

  function checkOverlap(newTag, existingTags) {
    for (let existing of existingTags) {
      // Check if rectangles overlap
      if (newTag.x < existing.x + existing.width &&
          newTag.x + newTag.width > existing.x &&
          newTag.y < existing.y + existing.height &&
          newTag.y + newTag.height > existing.y) {
        return true;
      }
    }
    return false;
  }

  function getRandomPosition(tagWidth, tagHeight) {
    const maxX = window.innerWidth - tagWidth - 20;
    const maxY = window.innerHeight - tagHeight - 20;
    const maxAttempts = 100; // Prevent infinite loops

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const position = {
        x: Math.floor(Math.random() * Math.max(maxX, 20)),
        y: Math.floor(Math.random() * Math.max(maxY, 20)),
        width: tagWidth,
        height: tagHeight
      };

      if (!checkOverlap(position, placedTags)) {
        placedTags.push(position);
        return position;
      }
    }

    // If we can't find a non-overlapping position, place it anyway
    const fallbackPosition = {
      x: Math.floor(Math.random() * Math.max(maxX, 20)),
      y: Math.floor(Math.random() * Math.max(maxY, 20)),
      width: tagWidth,
      height: tagHeight
    };
    placedTags.push(fallbackPosition);
    return fallbackPosition;
  }

  const cloud = document.getElementById("cloud");

  categories.forEach(cat => {
    const tag = document.createElement("div");
    tag.className = "tag";
    tag.textContent = cat.name;
    const fontSize = ((cat.count - minCount) / (maxCount - minCount)) * (maxFont - minFont) + minFont;
    tag.style.fontSize = `${fontSize}px`;
    tag.style.backgroundColor = getRandomColor();

    // Calculate approximate tag dimensions
    const tagWidth = getTextWidth(cat.name, fontSize);
    const tagHeight = fontSize + 16; // font size + padding

    // Set random non-overlapping position
    const position = getRandomPosition(tagWidth, tagHeight);
    tag.style.left = `${position.x}px`;
    tag.style.top = `${position.y}px`;

    cloud.appendChild(tag);
  });
</script>

</body>
</html>
