<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>User Categories Cloud</title>
<style>
  body {
    font-family: Arial, sans-serif;
    background: #f7f7f7;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
  }

  .cloud {
    position: relative;
    width: 100vw;
    height: 100vh;
  }

  .tag {
    position: absolute;
    padding: 8px 14px;
    border-radius: 8px;
    color: #fff;
    font-weight: bold;
    white-space: nowrap;
    transition: transform 0.2s ease;
  }

  .tag:hover {
    transform: scale(1.1);
    cursor: pointer;
  }
</style>
</head>
<body>

<div class="cloud" id="cloud"></div>

<script>
  const categories = [
    { name: "UX/UI Designer", count: 39 },
    { name: "Software Engineer", count: 33 },
    { name: "Designer", count: 11 },
    { name: "Frontend Developer", count: 9 },
    { name: "Professional", count: 8 },
    { name: "Web Developer", count: 7 },
    { name: "Student", count: 7 },
    { name: "Marketing Specialist", count: 7 },
    { name: "Mobile Developer", count: 6 },
    { name: "Graphic Designer", count: 6 },
    { name: "Freelancer", count: 5 },
    { name: "Product Owner", count: 4 },
    { name: "Penetration Tester", count: 4 },
    { name: "Data Scientist", count: 4 },
    { name: "Business Owner", count: 4 },
    { name: "Software Developer", count: 3 },
    { name: "DevOps Engineer", count: 3 },
    { name: "Cybersecurity Specialist", count: 3 },
    { name: "Consultant", count: 2 },
    { name: "IT Project Manager", count: 2 },
    { name: "Brand Manager", count: 2 },
    { name: "Video Editor", count: 2 },
    { name: "Project Manager", count: 2 },
    { name: "Coach", count: 1 },
    { name: "Sales Manager", count: 1 },
    { name: "Data Analyst", count: 1 },
    { name: "C# Developer", count: 1 },
    { name: "Backend Developer", count: 1 },
    { name: "SOC Analyst", count: 1 },
    { name: "Agile Coach", count: 1 },
    { name: "Game Designer", count: 1 },
    { name: "Legal Counsel", count: 1 },
    { name: "QA Engineer", count: 1 },
    { name: "Cross-Platform Mobile Developer", count: 1 },
    { name: "Content Writer", count: 1 },
    { name: "Flutter Developer", count: 1 },
    { name: "Data Engineer", count: 1 },
    { name: "Quality Assurance Engineer", count: 1 },
    { name: "iOS Developer", count: 1 },
    { name: "React Developer", count: 1 },
    { name: "Business Analyst", count: 1 },
    { name: "PR Specialist", count: 1 },
    { name: "Scrum Master", count: 1 },
    { name: "AI/ML Engineer", count: 1 },
    { name: "Python Developer", count: 1 }
  ];

  const minFont = 12;
  const maxFont = 40;
  const maxCount = Math.max(...categories.map(c => c.count));
  const minCount = Math.min(...categories.map(c => c.count));

  function getRandomColor() {
    return `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`;
  }

  function getTextWidth(text, fontSize) {
    // Approximate text width calculation
    return text.length * fontSize * 0.6 + 28; // 28px for padding
  }

  // Physics simulation variables
  const tags = [];
  const gravity = 0.3;
  const friction = 0.98;
  const bounce = 0.2;
  const rotationDamping = 0.95;
  const minVelocity = 0.1; // Minimum velocity threshold for movement

  class Tag {
    constructor(element, name, fontSize) {
      this.element = element;
      this.name = name;
      this.width = getTextWidth(name, fontSize);
      this.height = fontSize + 16;

      // Start from random position at top
      this.x = Math.random() * (window.innerWidth - this.width);
      this.y = -this.height - Math.random() * 200; // Start above screen

      // Physics properties
      this.vx = (Math.random() - 0.5) * 1; // Small random horizontal velocity
      this.vy = 0;
      this.mass = (this.width * this.height) / 1000; // Mass based on area

      // Rotation properties
      this.rotation = 0; // Current rotation in degrees
      this.angularVelocity = 0; // Rotation speed

      // Support tracking
      this.isSupported = false;
      this.supportingTags = [];
      this.restingOn = [];

      // Collision management
      this.collisionCooldown = 0;
      this.lastCollisionFrame = 0;
      this.isStable = false;
      this.stableFrames = 0;

      // Set initial position
      this.updatePosition();
    }

    updatePosition() {
      this.element.style.left = `${this.x}px`;
      this.element.style.top = `${this.y}px`;
      this.element.style.transform = `rotate(${this.rotation}deg)`;
      this.element.style.transformOrigin = 'center center';
    }

    getCenterX() {
      return this.x + this.width / 2;
    }

    getCenterY() {
      return this.y + this.height / 2;
    }

    getCorners() {
      const centerX = this.getCenterX();
      const centerY = this.getCenterY();
      const cos = Math.cos(this.rotation * Math.PI / 180);
      const sin = Math.sin(this.rotation * Math.PI / 180);
      const halfWidth = this.width / 2;
      const halfHeight = this.height / 2;

      return [
        // Top-left
        {
          x: centerX + (-halfWidth * cos - (-halfHeight) * sin),
          y: centerY + (-halfWidth * sin + (-halfHeight) * cos)
        },
        // Top-right
        {
          x: centerX + (halfWidth * cos - (-halfHeight) * sin),
          y: centerY + (halfWidth * sin + (-halfHeight) * cos)
        },
        // Bottom-right
        {
          x: centerX + (halfWidth * cos - halfHeight * sin),
          y: centerY + (halfWidth * sin + halfHeight * cos)
        },
        // Bottom-left
        {
          x: centerX + (-halfWidth * cos - halfHeight * sin),
          y: centerY + (-halfWidth * sin + halfHeight * cos)
        }
      ];
    }

    update() {
      // Decrease collision cooldown
      if (this.collisionCooldown > 0) {
        this.collisionCooldown--;
      }

      // Check stability
      this.checkStability();

      // Only apply physics if not stable
      if (!this.isStable) {
        // Check if tag is supported (has something underneath)
        this.checkSupport();

        // Apply gravity only if not fully supported
        if (!this.isSupported) {
          this.vy += gravity;
        }

        // Check center of gravity and apply tilting forces
        this.updateCenterOfGravity();

        // Apply velocities
        this.x += this.vx;
        this.y += this.vy;
        this.rotation += this.angularVelocity;

        // Apply damping
        this.vx *= friction;
        this.vy *= friction;
        this.angularVelocity *= rotationDamping;

        // Stop very small movements to prevent jitter
        if (Math.abs(this.vx) < minVelocity) this.vx = 0;
        if (Math.abs(this.vy) < minVelocity) this.vy = 0;
        if (Math.abs(this.angularVelocity) < 0.1) this.angularVelocity = 0;
      }

      // Boundary collisions
      const corners = this.getCorners();
      const minX = Math.min(...corners.map(c => c.x));
      const maxX = Math.max(...corners.map(c => c.x));
      const minY = Math.min(...corners.map(c => c.y));
      const maxY = Math.max(...corners.map(c => c.y));

      if (minX < 0) {
        this.x += -minX + 1;
        this.vx = Math.abs(this.vx) * bounce;
        this.isStable = false;
      }
      if (maxX > window.innerWidth) {
        this.x -= maxX - window.innerWidth + 1;
        this.vx = -Math.abs(this.vx) * bounce;
        this.isStable = false;
      }
      if (maxY > window.innerHeight) {
        this.y -= maxY - window.innerHeight + 1;
        this.vy = -Math.abs(this.vy) * bounce;
        this.isStable = false;
      }

      this.updatePosition();
    }

    checkStability() {
      const totalVelocity = Math.abs(this.vx) + Math.abs(this.vy) + Math.abs(this.angularVelocity);

      if (totalVelocity < minVelocity && this.isSupported) {
        this.stableFrames++;
        if (this.stableFrames > 30) { // Stable for 30 frames
          this.isStable = true;
          this.vx = 0;
          this.vy = 0;
          this.angularVelocity = 0;
        }
      } else {
        this.stableFrames = 0;
        this.isStable = false;
      }
    }

    checkSupport() {
      this.isSupported = false;
      this.restingOn = [];

      // Check if resting on ground
      const corners = this.getCorners();
      const bottomY = Math.max(...corners.map(c => c.y));

      if (bottomY >= window.innerHeight - 2) {
        this.isSupported = true;
        return;
      }

      // Check if resting on other tags
      for (let other of tags) {
        if (other === this) continue;

        if (this.isRestingOn(other)) {
          this.restingOn.push(other);
          this.isSupported = true;
        }
      }
    }

    isRestingOn(other) {
      const thisCorners = this.getCorners();
      const otherCorners = other.getCorners();

      const thisBottom = Math.max(...thisCorners.map(c => c.y));
      const otherTop = Math.min(...otherCorners.map(c => c.y));

      // Check if this tag's bottom is close to other tag's top
      if (Math.abs(thisBottom - otherTop) < 5) {
        // Check horizontal overlap
        const thisLeft = Math.min(...thisCorners.map(c => c.x));
        const thisRight = Math.max(...thisCorners.map(c => c.x));
        const otherLeft = Math.min(...otherCorners.map(c => c.x));
        const otherRight = Math.max(...otherCorners.map(c => c.x));

        return thisRight > otherLeft && thisLeft < otherRight;
      }
      return false;
    }

    updateCenterOfGravity() {
      if (!this.isSupported || this.restingOn.length === 0) return;

      const centerX = this.getCenterX();
      let supportLeft = Infinity;
      let supportRight = -Infinity;

      // Find the bounds of support
      for (let support of this.restingOn) {
        const supportCorners = support.getCorners();
        const left = Math.min(...supportCorners.map(c => c.x));
        const right = Math.max(...supportCorners.map(c => c.x));
        supportLeft = Math.min(supportLeft, left);
        supportRight = Math.max(supportRight, right);
      }

      // Check if center of gravity is outside support
      if (centerX < supportLeft || centerX > supportRight) {
        // Apply tilting force
        const tiltDirection = centerX < supportLeft ? -1 : 1;
        this.angularVelocity += tiltDirection * 0.5;

        // Apply sliding force
        this.vx += tiltDirection * 0.2;
        this.isSupported = false; // Start falling/sliding
      }
    }

    checkCollision(other) {
      const thisCorners = this.getCorners();
      const otherCorners = other.getCorners();

      // Simple AABB check with rotated rectangles
      const thisMinX = Math.min(...thisCorners.map(c => c.x));
      const thisMaxX = Math.max(...thisCorners.map(c => c.x));
      const thisMinY = Math.min(...thisCorners.map(c => c.y));
      const thisMaxY = Math.max(...thisCorners.map(c => c.y));

      const otherMinX = Math.min(...otherCorners.map(c => c.x));
      const otherMaxX = Math.max(...otherCorners.map(c => c.x));
      const otherMinY = Math.min(...otherCorners.map(c => c.y));
      const otherMaxY = Math.max(...otherCorners.map(c => c.y));

      return thisMinX < otherMaxX && thisMaxX > otherMinX &&
             thisMinY < otherMaxY && thisMaxY > otherMinY;
    }

    resolveCollision(other) {
      // Skip if either tag is in collision cooldown
      if (this.collisionCooldown > 0 || other.collisionCooldown > 0) {
        return;
      }

      // Get bounding boxes for both rotated rectangles
      const thisCorners = this.getCorners();
      const otherCorners = other.getCorners();

      const thisMinX = Math.min(...thisCorners.map(c => c.x));
      const thisMaxX = Math.max(...thisCorners.map(c => c.x));
      const thisMinY = Math.min(...thisCorners.map(c => c.y));
      const thisMaxY = Math.max(...thisCorners.map(c => c.y));

      const otherMinX = Math.min(...otherCorners.map(c => c.x));
      const otherMaxX = Math.max(...otherCorners.map(c => c.x));
      const otherMinY = Math.min(...otherCorners.map(c => c.y));
      const otherMaxY = Math.max(...otherCorners.map(c => c.y));

      // Calculate overlap
      const overlapX = Math.min(thisMaxX - otherMinX, otherMaxX - thisMinX);
      const overlapY = Math.min(thisMaxY - otherMinY, otherMaxY - thisMinY);

      // Only resolve if there's significant overlap
      if (overlapX < 2 && overlapY < 2) return;

      // Set collision cooldown to prevent immediate re-collision
      this.collisionCooldown = 10;
      other.collisionCooldown = 10;

      // Mark both as unstable
      this.isStable = false;
      other.isStable = false;
      this.stableFrames = 0;
      other.stableFrames = 0;

      // Separate to prevent overlap
      if (overlapX < overlapY) {
        // Horizontal separation
        const separationX = overlapX / 2 + 2; // +2 for better separation
        if (this.getCenterX() < other.getCenterX()) {
          this.x -= separationX;
          other.x += separationX;
        } else {
          this.x += separationX;
          other.x -= separationX;
        }

        // Simple velocity exchange with reduced bounce
        const avgVx = (this.vx + other.vx) / 2;
        this.vx = (this.getCenterX() < other.getCenterX() ? -1 : 1) * Math.abs(avgVx) * bounce;
        other.vx = (other.getCenterX() < this.getCenterX() ? -1 : 1) * Math.abs(avgVx) * bounce;

      } else {
        // Vertical separation
        const separationY = overlapY / 2 + 2; // +2 for better separation
        if (this.getCenterY() < other.getCenterY()) {
          this.y -= separationY;
          other.y += separationY;
        } else {
          this.y += separationY;
          other.y -= separationY;
        }

        // Simple velocity exchange with reduced bounce
        const avgVy = (this.vy + other.vy) / 2;
        this.vy = (this.getCenterY() < other.getCenterY() ? -1 : 1) * Math.abs(avgVy) * bounce;
        other.vy = (other.getCenterY() < this.getCenterY() ? -1 : 1) * Math.abs(avgVy) * bounce;
      }

      // Add minimal rotational effects
      this.angularVelocity += (Math.random() - 0.5) * 1;
      other.angularVelocity += (Math.random() - 0.5) * 1;
    }
  }

  // Animation loop
  function animate() {
    // Update all tags
    for (let tag of tags) {
      tag.update();
    }

    // Check collisions between all tags
    for (let i = 0; i < tags.length; i++) {
      for (let j = i + 1; j < tags.length; j++) {
        if (tags[i].checkCollision(tags[j])) {
          tags[i].resolveCollision(tags[j]);
        }
      }
    }

    requestAnimationFrame(animate);
  }

  const cloud = document.getElementById("cloud");

  // Create tags with physics
  categories.forEach((cat, index) => {
    setTimeout(() => {
      const tagElement = document.createElement("div");
      tagElement.className = "tag";
      tagElement.textContent = cat.name;
      const fontSize = ((cat.count - minCount) / (maxCount - minCount)) * (maxFont - minFont) + minFont;
      tagElement.style.fontSize = `${fontSize}px`;
      tagElement.style.backgroundColor = getRandomColor();

      cloud.appendChild(tagElement);

      // Create physics tag
      const tag = new Tag(tagElement, cat.name, fontSize);
      tags.push(tag);
    }, index * 100); // Stagger the drops
  });

  // Start animation
  animate();
</script>

</body>
</html>
