<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>User Categories Cloud</title>
<style>
  body {
    font-family: Arial, sans-serif;
    background: #f7f7f7;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
  }

  .cloud {
    position: relative;
    width: 100vw;
    height: 100vh;
  }

  .tag {
    position: absolute;
    padding: 8px 14px;
    border-radius: 8px;
    color: #fff;
    font-weight: bold;
    white-space: nowrap;
    transition: transform 0.2s ease;
  }

  .tag:hover {
    transform: scale(1.1);
    cursor: pointer;
  }
</style>
</head>
<body>

<div class="cloud" id="cloud"></div>

<script>
  const categories = [
    { name: "UX/UI Designer", count: 39 },
    { name: "Software Engineer", count: 33 },
    { name: "Designer", count: 11 },
    { name: "Frontend Developer", count: 9 },
    { name: "Professional", count: 8 },
    { name: "Web Developer", count: 7 },
    { name: "Student", count: 7 },
    { name: "Marketing Specialist", count: 7 },
    { name: "Mobile Developer", count: 6 },
    { name: "Graphic Designer", count: 6 },
    { name: "Freelancer", count: 5 },
    { name: "Product Owner", count: 4 },
    { name: "Penetration Tester", count: 4 },
    { name: "Data Scientist", count: 4 },
    { name: "Business Owner", count: 4 },
    { name: "Software Developer", count: 3 },
    { name: "DevOps Engineer", count: 3 },
    { name: "Cybersecurity Specialist", count: 3 },
    { name: "Consultant", count: 2 },
    { name: "IT Project Manager", count: 2 },
    { name: "Brand Manager", count: 2 },
    { name: "Video Editor", count: 2 },
    { name: "Project Manager", count: 2 },
    { name: "Coach", count: 1 },
    { name: "Sales Manager", count: 1 },
    { name: "Data Analyst", count: 1 },
    { name: "C# Developer", count: 1 },
    { name: "Backend Developer", count: 1 },
    { name: "SOC Analyst", count: 1 },
    { name: "Agile Coach", count: 1 },
    { name: "Game Designer", count: 1 },
    { name: "Legal Counsel", count: 1 },
    { name: "QA Engineer", count: 1 },
    { name: "Cross-Platform Mobile Developer", count: 1 },
    { name: "Content Writer", count: 1 },
    { name: "Flutter Developer", count: 1 },
    { name: "Data Engineer", count: 1 },
    { name: "Quality Assurance Engineer", count: 1 },
    { name: "iOS Developer", count: 1 },
    { name: "React Developer", count: 1 },
    { name: "Business Analyst", count: 1 },
    { name: "PR Specialist", count: 1 },
    { name: "Scrum Master", count: 1 },
    { name: "AI/ML Engineer", count: 1 },
    { name: "Python Developer", count: 1 }
  ];

  const minFont = 12;
  const maxFont = 40;
  const maxCount = Math.max(...categories.map(c => c.count));
  const minCount = Math.min(...categories.map(c => c.count));

  function getRandomColor() {
    return `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`;
  }

  function getTextWidth(text, fontSize) {
    // Approximate text width calculation
    return text.length * fontSize * 0.6 + 28; // 28px for padding
  }

  // Physics simulation variables
  const tags = [];
  const gravity = 0.5;
  const friction = 0.98;
  const bounce = 0.7;

  class Tag {
    constructor(element, name, fontSize) {
      this.element = element;
      this.name = name;
      this.width = getTextWidth(name, fontSize);
      this.height = fontSize + 16;

      // Start from random position at top
      this.x = Math.random() * (window.innerWidth - this.width);
      this.y = -this.height - Math.random() * 200; // Start above screen

      // Physics properties
      this.vx = (Math.random() - 0.5) * 2; // Small random horizontal velocity
      this.vy = 0;
      this.mass = fontSize / 10; // Mass based on font size

      // Set initial position
      this.updatePosition();
    }

    updatePosition() {
      this.element.style.left = `${this.x}px`;
      this.element.style.top = `${this.y}px`;
    }

    update() {
      // Apply gravity
      this.vy += gravity;

      // Apply velocity
      this.x += this.vx;
      this.y += this.vy;

      // Apply friction
      this.vx *= friction;
      this.vy *= friction;

      // Boundary collisions
      if (this.x < 0) {
        this.x = 0;
        this.vx *= -bounce;
      }
      if (this.x + this.width > window.innerWidth) {
        this.x = window.innerWidth - this.width;
        this.vx *= -bounce;
      }
      if (this.y + this.height > window.innerHeight) {
        this.y = window.innerHeight - this.height;
        this.vy *= -bounce;
      }

      this.updatePosition();
    }

    checkCollision(other) {
      return this.x < other.x + other.width &&
             this.x + this.width > other.x &&
             this.y < other.y + other.height &&
             this.y + this.height > other.y;
    }

    resolveCollision(other) {
      // Calculate overlap
      const overlapX = Math.min(this.x + this.width - other.x, other.x + other.width - this.x);
      const overlapY = Math.min(this.y + this.height - other.y, other.y + other.height - this.y);

      // Separate based on smallest overlap
      if (overlapX < overlapY) {
        // Horizontal separation
        if (this.x < other.x) {
          this.x -= overlapX / 2;
          other.x += overlapX / 2;
        } else {
          this.x += overlapX / 2;
          other.x -= overlapX / 2;
        }

        // Exchange velocities with mass consideration
        const totalMass = this.mass + other.mass;
        const newVx1 = (this.vx * (this.mass - other.mass) + 2 * other.mass * other.vx) / totalMass;
        const newVx2 = (other.vx * (other.mass - this.mass) + 2 * this.mass * this.vx) / totalMass;
        this.vx = newVx1 * bounce;
        other.vx = newVx2 * bounce;
      } else {
        // Vertical separation
        if (this.y < other.y) {
          this.y -= overlapY / 2;
          other.y += overlapY / 2;
        } else {
          this.y += overlapY / 2;
          other.y -= overlapY / 2;
        }

        // Exchange velocities with mass consideration
        const totalMass = this.mass + other.mass;
        const newVy1 = (this.vy * (this.mass - other.mass) + 2 * other.mass * other.vy) / totalMass;
        const newVy2 = (other.vy * (other.mass - this.mass) + 2 * this.mass * this.vy) / totalMass;
        this.vy = newVy1 * bounce;
        other.vy = newVy2 * bounce;
      }
    }
  }

  // Animation loop
  function animate() {
    // Update all tags
    for (let tag of tags) {
      tag.update();
    }

    // Check collisions between all tags
    for (let i = 0; i < tags.length; i++) {
      for (let j = i + 1; j < tags.length; j++) {
        if (tags[i].checkCollision(tags[j])) {
          tags[i].resolveCollision(tags[j]);
        }
      }
    }

    requestAnimationFrame(animate);
  }

  const cloud = document.getElementById("cloud");

  // Create tags with physics
  categories.forEach((cat, index) => {
    setTimeout(() => {
      const tagElement = document.createElement("div");
      tagElement.className = "tag";
      tagElement.textContent = cat.name;
      const fontSize = ((cat.count - minCount) / (maxCount - minCount)) * (maxFont - minFont) + minFont;
      tagElement.style.fontSize = `${fontSize}px`;
      tagElement.style.backgroundColor = getRandomColor();

      cloud.appendChild(tagElement);

      // Create physics tag
      const tag = new Tag(tagElement, cat.name, fontSize);
      tags.push(tag);
    }, index * 100); // Stagger the drops
  });

  // Start animation
  animate();
</script>

</body>
</html>
