<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>User Categories Cloud</title>
<style>
  body {
    font-family: Arial, sans-serif;
    background: #f7f7f7;
    display: flex;
    justify-content: center;
    padding: 20px;
  }

  .cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    max-width: 1000px;
  }

  .tag {
    padding: 8px 14px;
    border-radius: 8px;
    color: #fff;
    font-weight: bold;
    white-space: nowrap;
    transition: transform 0.2s ease;
  }

  .tag:hover {
    transform: scale(1.1);
    cursor: pointer;
  }
</style>
</head>
<body>

<div class="cloud" id="cloud"></div>

<script>
  const categories = [
    { name: "UX/UI Designer", count: 39 },
    { name: "Software Engineer", count: 33 },
    { name: "Designer", count: 11 },
    { name: "Frontend Developer", count: 9 },
    { name: "Professional", count: 8 },
    { name: "Web Developer", count: 7 },
    { name: "Student", count: 7 },
    { name: "Marketing Specialist", count: 7 },
    { name: "Mobile Developer", count: 6 },
    { name: "Graphic Designer", count: 6 },
    { name: "Freelancer", count: 5 },
    { name: "Product Owner", count: 4 },
    { name: "Penetration Tester", count: 4 },
    { name: "Data Scientist", count: 4 },
    { name: "Business Owner", count: 4 },
    { name: "Software Developer", count: 3 },
    { name: "DevOps Engineer", count: 3 },
    { name: "Cybersecurity Specialist", count: 3 },
    { name: "Consultant", count: 2 },
    { name: "IT Project Manager", count: 2 },
    { name: "Brand Manager", count: 2 },
    { name: "Video Editor", count: 2 },
    { name: "Project Manager", count: 2 },
    { name: "Coach", count: 1 },
    { name: "Sales Manager", count: 1 },
    { name: "Data Analyst", count: 1 },
    { name: "C# Developer", count: 1 },
    { name: "Backend Developer", count: 1 },
    { name: "SOC Analyst", count: 1 },
    { name: "Agile Coach", count: 1 },
    { name: "Game Designer", count: 1 },
    { name: "Legal Counsel", count: 1 },
    { name: "QA Engineer", count: 1 },
    { name: "Cross-Platform Mobile Developer", count: 1 },
    { name: "Content Writer", count: 1 },
    { name: "Flutter Developer", count: 1 },
    { name: "Data Engineer", count: 1 },
    { name: "Quality Assurance Engineer", count: 1 },
    { name: "iOS Developer", count: 1 },
    { name: "React Developer", count: 1 },
    { name: "Business Analyst", count: 1 },
    { name: "PR Specialist", count: 1 },
    { name: "Scrum Master", count: 1 },
    { name: "AI/ML Engineer", count: 1 },
    { name: "Python Developer", count: 1 }
  ];

  const minFont = 12;
  const maxFont = 40;
  const maxCount = Math.max(...categories.map(c => c.count));
  const minCount = Math.min(...categories.map(c => c.count));

  function getRandomColor() {
    return `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`;
  }

  const cloud = document.getElementById("cloud");

  categories.forEach(cat => {
    const tag = document.createElement("div");
    tag.className = "tag";
    tag.textContent = cat.name;
    tag.style.fontSize = `${((cat.count - minCount) / (maxCount - minCount)) * (maxFont - minFont) + minFont}px`;
    tag.style.backgroundColor = getRandomColor();
    cloud.appendChild(tag);
  });
</script>

</body>
</html>
